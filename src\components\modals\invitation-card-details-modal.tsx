import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { CloseCircle, Calendar, Clock } from 'iconsax-react';
import { DayPicker } from 'react-day-picker';
import 'react-day-picker/dist/style.css';
import { format } from 'date-fns';
import { AddressAutocomplete } from '../inputs/address-autocomplete';

interface TemplateItem {
  id?: string;
  name: string;
  preview_url?: string;
  image?: string;
}

interface InvitationFormData {
  title: string;
  subtitle: string;
  introduction: string;
  eventDate: string;
  eventTime: string;
  eventVenue: string;
  eventVenuePlaceId: string;
  footer: string;
}

interface InvitationCardDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  selectedTemplate: TemplateItem | null;
  onContinue: (formData: InvitationFormData) => void;
}

export const InvitationCardDetailsModal: React.FC<
  InvitationCardDetailsModalProps
> = ({ isOpen, onClose, selectedTemplate, onContinue }) => {
  const [formData, setFormData] = useState<InvitationFormData>({
    title: '',
    subtitle: '',
    introduction: '',
    eventDate: '',
    eventTime: '',
    eventVenue: '',
    eventVenuePlaceId: '',
    footer: '',
  });

  // Date picker state
  const [dateRange, setDateRange] = useState<{
    from: Date | undefined;
    to: Date | undefined;
  }>({
    from: undefined,
    to: undefined,
  });
  const [dateRangeText, setDateRangeText] = useState('');
  const [showDatePicker, setShowDatePicker] = useState(false);

  // Time picker state
  const [showTimePicker, setShowTimePicker] = useState(false);
  const timePickerRef = useRef<HTMLDivElement>(null);

  // Time options
  const timeOptions = [
    '12:00 AM',
    '12:30 AM',
    '1:00 AM',
    '1:30 AM',
    '2:00 AM',
    '2:30 AM',
    '3:00 AM',
    '3:30 AM',
    '4:00 AM',
    '4:30 AM',
    '5:00 AM',
    '5:30 AM',
    '6:00 AM',
    '6:30 AM',
    '7:00 AM',
    '7:30 AM',
    '8:00 AM',
    '8:30 AM',
    '9:00 AM',
    '9:30 AM',
    '10:00 AM',
    '10:30 AM',
    '11:00 AM',
    '11:30 AM',
    '12:00 PM',
    '12:30 PM',
    '1:00 PM',
    '1:30 PM',
    '2:00 PM',
    '2:30 PM',
    '3:00 PM',
    '3:30 PM',
    '4:00 PM',
    '4:30 PM',
    '5:00 PM',
    '5:30 PM',
    '6:00 PM',
    '6:30 PM',
    '7:00 PM',
    '7:30 PM',
    '8:00 PM',
    '8:30 PM',
    '9:00 PM',
    '9:30 PM',
    '10:00 PM',
    '10:30 PM',
    '11:00 PM',
    '11:30 PM',
  ];

  // Reset form when modal opens
  useEffect(() => {
    if (isOpen) {
      setFormData({
        title: '',
        subtitle: '',
        introduction: '',
        eventDate: '',
        eventTime: '',
        eventVenue: '',
        eventVenuePlaceId: '',
        footer: '',
      });
    }
  }, [isOpen]);

  // Handle escape key and body scroll
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, onClose]);

  const handleInputChange = (
    field: keyof InvitationFormData,
    value: string
  ) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  // Date picker handlers
  const handleDateRangeSelect = (
    range: { from: Date | undefined; to?: Date | undefined } | undefined
  ) => {
    if (range) {
      const normalizedRange = {
        from: range.from,
        to: range.to || undefined,
      };
      setDateRange(normalizedRange);

      if (normalizedRange.from && normalizedRange.to) {
        const startText = format(normalizedRange.from, 'MMM dd');
        const endText = format(normalizedRange.to, 'MMM dd, yyyy');
        setDateRangeText(`${startText} - ${endText}`);
        handleInputChange('eventDate', `${startText} - ${endText}`);
      } else if (normalizedRange.from) {
        const startText = format(normalizedRange.from, 'MMM dd, yyyy');
        setDateRangeText(startText);
        handleInputChange('eventDate', startText);
      } else {
        setDateRangeText('');
        handleInputChange('eventDate', '');
      }
    } else {
      setDateRange({ from: undefined, to: undefined });
      setDateRangeText('');
      handleInputChange('eventDate', '');
    }
  };

  const handleTimeSelect = (time: string) => {
    handleInputChange('eventTime', time);
    setShowTimePicker(false);
  };

  const handleLocationChange = (value: string, placeId?: string) => {
    handleInputChange('eventVenue', value);
    if (placeId) {
      handleInputChange('eventVenuePlaceId', placeId);
    }
  };

  const handleContinue = () => {
    onContinue(formData);
  };

  const isFormValid =
    formData.title.trim() &&
    formData.eventDate &&
    formData.eventTime &&
    formData.eventVenue.trim() &&
    formData.eventVenuePlaceId.trim();

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        transition={{ duration: 0.2 }}
        className="fixed inset-0 z-50 flex items-center justify-center bg-black/40 backdrop-blur-sm"
        onClick={onClose}>
        <motion.div
          initial={{ opacity: 0, scale: 0.95, y: 20 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.95, y: 20 }}
          transition={{ duration: 0.2 }}
          className="mx-4 w-full max-w-[1056px] max-h-[90vh] overflow-y-scroll [&::-webkit-scrollbar]:w-0 relative rounded-2xl bg-white shadow-xl sm:mx-0"
          onClick={(e) => e.stopPropagation()}>
          <button
            onClick={onClose}
            className="absolute right-4 top-4 z-10 rounded-full p-1 hover:bg-gray-100 transition-colors">
            <CloseCircle size={32} variant="Bulk" color="#4D55F2" />
          </button>
          <div className="flex  overflow-hidden rounded-2xl">
            {/* Left Side - Template Preview */}
            <div className="w-1/2  p-8 flex items-center justify-center">
              {selectedTemplate?.preview_url ? (
                <div className="w-full max-w-[446px]  bg-gradient-to-br from-purple-50 to-blue-50 p-6 aspect-[3/4] overflow-hidden rounded-[16px]">
                  <img
                    src={selectedTemplate.preview_url}
                    alt={selectedTemplate.name}
                    className="w-full h-full object-cover rounded-xl border-4 border-white -rotate-2"
                  />
                </div>
              ) : (
                <div className="w-full max-w-[280px] aspect-[3/4] rounded-xl bg-gray-200 flex items-center justify-center">
                  <span className="text-gray-500">Template Preview</span>
                </div>
              )}
            </div>

            <div className="w-1/2 py-8 pr-8 overflow-y-auto">
              <div className="mb-6">
                <h2 className="text-2xl md:text-[32px] font-semibold text-gray-900">
                  Invitation Card{' '}
                  <span className="text-[#999999]">Details</span>
                </h2>
                <p className="text-gray-600">
                  Please enter the details you want displayed on your invite{' '}
                </p>
              </div>

              <div className="space-y-5">
                {/* Title */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Title
                  </label>
                  <input
                    type="text"
                    value={formData.title}
                    onChange={(e) => handleInputChange('title', e.target.value)}
                    placeholder="Enter Event Title"
                    className="w-full py-2.5 px-3.5 rounded-full border border-grey-200 placeholder:text-grey-300 outline-none text-base"
                  />
                </div>

                {/* Subtitle */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Subtitle
                  </label>
                  <textarea
                    value={formData.subtitle}
                    onChange={(e) =>
                      handleInputChange('subtitle', e.target.value)
                    }
                    placeholder="Write a brief description here"
                    rows={2}
                    className="w-full py-2 px-3.5 rounded-xl border border-grey-200 placeholder:text-grey-300 outline-none text-base resize-none"
                  />
                </div>

                {/* Introduction */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Introduction
                  </label>
                  <input
                    type="text"
                    value={formData.introduction}
                    onChange={(e) =>
                      handleInputChange('introduction', e.target.value)
                    }
                    placeholder="Enter Event Title"
                    className="w-full py-2.5 px-3.5 rounded-full border border-grey-200 placeholder:text-grey-300 outline-none text-base"
                  />
                </div>

                {/* Event Date and Time */}
                <div className="flex gap-3">
                  {/* Event Date */}
                  <div className="">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Event Date
                    </label>
                    <div className="relative">
                      <input
                        type="text"
                        value={dateRangeText}
                        readOnly
                        onClick={() => setShowDatePicker(true)}
                        className="w-full py-2.5 px-3.5 pl-10 rounded-full border border-grey-200 placeholder:text-grey-300 outline-none text-base cursor-pointer"
                        placeholder="Select date"
                      />
                      <Calendar
                        size={18}
                        color="#4D55F2"
                        variant="Bulk"
                        className="absolute left-3 top-1/2 transform -translate-y-1/2 cursor-pointer"
                        onClick={() => setShowDatePicker(true)}
                      />
                    </div>
                  </div>

                  {/* Time of Event */}
                  <div className="">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Event Time
                    </label>
                    <div className="relative">
                      <input
                        type="text"
                        value={formData.eventTime}
                        readOnly
                        onClick={() => setShowTimePicker(!showTimePicker)}
                        className="w-full py-2.5 pl-10  rounded-full border border-grey-200 placeholder:text-grey-300 outline-none text-base cursor-pointer"
                        placeholder="Select time"
                      />
                      <Clock
                        size={18}
                        color="#4D55F2"
                        variant="Bulk"
                        className="absolute left-3 top-1/2 transform -translate-y-1/2 cursor-pointer"
                        onClick={() => setShowTimePicker(!showTimePicker)}
                      />

                      {/* Time Picker Dropdown */}
                      {showTimePicker && (
                        <div
                          ref={timePickerRef}
                          className="absolute z-10 mt-1 bg-white rounded-xl shadow-lg border border-gray-200 left-0 w-full max-h-[200px] overflow-y-auto">
                          <div className="py-2">
                            {timeOptions.map((time) => (
                              <button
                                key={time}
                                onClick={() => handleTimeSelect(time)}
                                className={`w-full text-left px-4 py-2 text-sm hover:bg-gray-50 ${
                                  formData.eventTime === time
                                    ? 'bg-blue-50 text-blue-600 font-medium'
                                    : 'text-gray-700'
                                }`}>
                                {time}
                              </button>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
                {/* Event Venue */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Event Venue
                  </label>
                  <AddressAutocomplete
                    value={formData.eventVenue}
                    onChange={handleLocationChange}
                    placeholder="Type your location"
                    className="w-full py-2.5 px-3.5 rounded-full border border-grey-200 placeholder:text-grey-300 outline-none text-base"
                  />
                </div>

                {/* Footer */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Footer
                  </label>
                  <input
                    type="text"
                    value={formData.footer}
                    onChange={(e) =>
                      handleInputChange('footer', e.target.value)
                    }
                    placeholder="Enter Event Title"
                    className="w-full py-2.5 px-3.5 rounded-full border border-grey-200 placeholder:text-grey-300 outline-none text-base"
                  />
                </div>
              </div>

              {/* Continue Button */}
              <div className="mt-8 flex justify-end">
                <button
                  onClick={handleContinue}
                  disabled={!isFormValid}
                  className={`px-8 py-3 rounded-full font-semibold text-white transition-all duration-200 ${
                    isFormValid
                      ? 'bg-primary-650 hover:bg-primary-650 cursor-pointer'
                      : 'bg-primary-650/50 cursor-not-allowed'
                  }`}>
                  Continue 
                </button>
              </div>
            </div>
          </div>
        </motion.div>
      </motion.div>

      {/* Date Picker Modal */}
      {showDatePicker && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.2 }}
          className="fixed inset-0 z-[60] flex items-center justify-center bg-black/40 backdrop-blur-sm"
          onClick={() => setShowDatePicker(false)}>
          <motion.div
            initial={{ opacity: 0, scale: 0.95, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: 20 }}
            transition={{ duration: 0.2 }}
            className="mx-4 max-h-[90vh] w-full max-w-[522px] relative overflow-y-scroll lg:overflow-hidden rounded-2xl bg-white shadow-xl sm:mx-0"
            onClick={(e) => e.stopPropagation()}>
            <div className="text-center flex-1 pr-6 mt-8">
              <h3 className="text-[28px] font-medium text-dark-200">
                Event Date
              </h3>
              <p className="mt-1 text-base text-grey-250">
                Please choose the date(s) for your event
              </p>
            </div>
            <button
              onClick={() => setShowDatePicker(false)}
              className="absolute top-4 right-4 transition-colors">
              <CloseCircle size="33" color="#4D55F2" variant="Bulk" />
            </button>
            <div className="p-6 flex justify-center">
              <style>
                {`
                  .rdp {
                    --rdp-cell-size: 40px;
                    --rdp-accent-color: #4D55F2;
                    --rdp-background-color: #4D55F2;
                    margin: 0;
                  }
                  .rdp-months {
                    display: flex;
                    justify-content: center;
                  }
                  .rdp-month {
                    margin: 0 1em;
                  }
                  .rdp-table {
                    margin: 0;
                  }
                  .rdp-head_cell {
                    font-weight: 500;
                    font-size: 0.875rem;
                    color: #6B7280;
                  }
                  .rdp-cell {
                    padding: 0;
                  }
                  .rdp-button {
                    border: none;
                    background: none;
                    font-size: 0.875rem;
                    cursor: pointer;
                    width: var(--rdp-cell-size);
                    height: var(--rdp-cell-size);
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                  }
                  .rdp-day_selected {
                    background-color: var(--rdp-accent-color);
                    color: white;
                  }
                  .rdp-day_today {
                    color: var(--rdp-accent-color);
                    font-weight: bold;
                  }
                  .rdp-nav {
                    display: flex;
                    align-items: center;
                  }
                `}
              </style>
              <DayPicker
                mode="range"
                selected={dateRange}
                onSelect={handleDateRangeSelect}
                disabled={{ before: new Date() }}
                startMonth={new Date()}
                className="w-full flex justify-center"
              />
            </div>

            <div className="flex items-center justify-between pb-10 pt-2 px-6">
              <button
                onClick={() => setShowDatePicker(false)}
                className="bg-primary-650 hover:bg-primary-650/90 w-full rounded-full md:mx-20 h-[44px] text-sm font-medium text-white transition-colors">
                Continue
              </button>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};
